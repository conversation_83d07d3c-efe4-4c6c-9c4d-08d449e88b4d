
services:
  
  # API Gateway (Deno)
  gateway:
    build:
      context: ./server/gateway
      dockerfile: Dockerfile
    container_name: aery_gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************************/aery_db
      - REDIS_URL=redis://redis:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - API_SECRET_KEY=${API_SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - DENO_ENV=development
    volumes:
      - ./server/gateway:/app
      - ./shared:/app/shared
    networks:
      - aery_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python Workers
  workers:
    build:
      context: ./server/workers
      dockerfile: Dockerfile
    container_name: aery_workers
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=**************************************************/aery_db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PLAYWRIGHT_HEADLESS=true
      - PYTHON_ENV=development
    volumes:
      - ./server/workers:/app
      - ./shared:/app/shared
      - ./artifacts:/app/artifacts
      - /dev/shm:/dev/shm
    networks:
      - aery_network
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    deploy:
      replicas: 2
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://redis:6379')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aery_postgres
    environment:
      POSTGRES_DB: aery_db
      POSTGRES_USER: aery_user
      POSTGRES_PASSWORD: aery_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./shared/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - aery_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aery_user -d aery_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Queues and Cache
  redis:
    image: redis:7-alpine
    container_name: aery_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/monitoring/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - aery_network
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: aery_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/conf.d:/etc/nginx/conf.d
    networks:
      - aery_network
    depends_on:
      - gateway
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: aery_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - aery_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: aery_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - aery_network
    depends_on:
      - prometheus
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  aery_network:
    driver: bridge