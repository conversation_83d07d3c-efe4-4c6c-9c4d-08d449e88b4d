# AERY Project Makefile
# Optimized for OrbStack development workflow

.PHONY: help dev-setup dev dev-orbstack prod build clean test lint fmt check install logs status health gateway workers db-reset db-migrate db-seed dev-logs orbstack docker monitor

# Default target
help: ## Show this help message
	@echo "AERY Browser Automation API - Development Commands"
	@echo "=================================================="
	@echo ""
	@echo "Quick Setup:"
	@echo "  dev-setup        Complete development environment setup (one-time)"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev              Start development environment (standard Docker)"
	@echo "  dev-orbstack     Start development environment (OrbStack optimized)"
	@echo "  prod             Start production environment"
	@echo ""
	@echo "Build Commands:"
	@echo "  build            Build all Docker images"
	@echo "  build-gateway    Build gateway image only"
	@echo "  build-workers    Build workers image only"
	@echo ""
	@echo "Management Commands:"
	@echo "  install          Install dependencies for all services"
	@echo "  test             Run tests for all services"
	@echo "  lint             Run linting for all services"
	@echo "  fmt              Format code for all services"
	@echo "  check            Run all checks (fmt, lint, test)"
	@echo ""
	@echo "Monitoring Commands:"
	@echo "  logs             Show logs for all services"
	@echo "  logs-gateway     Show gateway logs"
	@echo "  logs-workers     Show workers logs"
	@echo "  status           Show service status"
	@echo "  health           Check service health"
	@echo ""
	@echo "Cleanup Commands:"
	@echo "  stop             Stop all services"
	@echo "  clean            Stop and remove containers, networks, volumes"
	@echo "  reset            Complete reset (clean + remove images)"
	@echo ""

# Quick Development Setup
dev-setup: ## Complete development environment setup (one-time)
	@echo "🚀 Setting up AERY development environment..."
	@echo ""
	@echo "📋 Checking prerequisites..."
	@command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed. Please install Docker first."; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose is required but not installed. Please install Docker Compose first."; exit 1; }
	@echo "✅ Docker and Docker Compose are installed"
	@echo ""
	@echo "📁 Setting up environment file..."
	@if [ ! -f .env ]; then \
		if [ -f .env.example ]; then \
			cp .env.example .env; \
			echo "✅ Created .env from .env.example"; \
		else \
			echo "⚠️  .env.example not found, creating basic .env"; \
			echo "# AERY Environment Configuration" > .env; \
			echo "NODE_ENV=development" >> .env; \
			echo "DATABASE_URL=postgresql://aery:aery_dev_password@localhost:5432/aery" >> .env; \
			echo "REDIS_URL=redis://localhost:6379" >> .env; \
			echo "JWT_SECRET=your-super-secret-jwt-key-change-in-production" >> .env; \
		fi; \
	else \
		echo "✅ .env file already exists"; \
	fi
	@echo ""
	@echo "🐳 Building Docker images..."
	@$(MAKE) build
	@echo ""
	@echo "🗄️  Setting up database..."
	@docker-compose -f docker-compose.dev.yml up -d postgres redis
	@echo "⏳ Waiting for database to be ready..."
	@sleep 10
	@echo ""
	@echo "🎉 Setup complete! You can now run:"
	@echo "   make dev          # Start all services"
	@echo "   make dev-orbstack # Start with OrbStack optimization (macOS)"
	@echo ""
	@echo "🌐 Services will be available at:"
	@echo "   API Gateway:   http://localhost:8000"
	@echo "   PgAdmin:       http://localhost:8080"
	@echo "   Redis UI:      http://localhost:8081"
	@echo "   MailHog:       http://localhost:8025"

# Development Environment
dev: ## Start development environment (standard Docker)
	@echo "🚀 Starting AERY development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development environment started!"
	@echo "🔗 Gateway: http://localhost:8000"
	@echo "🗄️  Database UI: http://localhost:8080"
	@echo "📧 Redis UI: http://localhost:8081"
	@echo "📬 Mail UI: http://localhost:8025"

dev-orbstack: ## Start development environment (OrbStack optimized)
	@echo "🚀 Starting AERY development environment (OrbStack optimized)..."
	docker-compose -f docker-compose.orbstack.yml up -d
	@echo "✅ OrbStack development environment started!"
	@echo "🔗 Gateway: http://gateway.orb.local:8000"
	@echo "🗄️  Database UI: http://pgadmin.orb.local:8080"
	@echo "📧 Redis UI: http://redis-ui.orb.local:8081"
	@echo "📬 Mail UI: http://mail.orb.local:8025"

prod: ## Start production environment
	@echo "🚀 Starting AERY production environment..."
	docker-compose up -d
	@echo "✅ Production environment started!"
	@echo "🌐 Application: http://localhost"
	@echo "📊 Monitoring: http://localhost:3001"

# Build Commands
build: ## Build all Docker images
	@echo "🔨 Building all Docker images..."
	docker-compose build --parallel
	@echo "✅ All images built successfully!"

build-gateway: ## Build gateway image only
	@echo "🔨 Building gateway image..."
	docker-compose build gateway
	@echo "✅ Gateway image built!"

build-workers: ## Build workers image only
	@echo "🔨 Building workers image..."
	docker-compose build workers
	@echo "✅ Workers image built!"

# Development Commands
install: ## Install dependencies for all services
	@echo "📦 Installing dependencies..."
	@echo "Installing gateway dependencies..."
	cd server/gateway && deno cache main.ts
	@echo "Installing worker dependencies..."
	cd server/workers && pip install -r requirements.txt
	@echo "✅ All dependencies installed!"

test: ## Run tests for all services
	@echo "🧪 Running tests..."
	@echo "Testing gateway..."
	cd server/gateway && deno test -A
	@echo "Testing workers..."
	cd server/workers && python -m pytest
	@echo "✅ All tests passed!"

lint: ## Run linting for all services
	@echo "🔍 Running linting..."
	@echo "Linting gateway..."
	cd server/gateway && deno lint
	@echo "Linting workers..."
	cd server/workers && python -m flake8 src/
	@echo "✅ All linting passed!"

fmt: ## Format code for all services
	@echo "✨ Formatting code..."
	@echo "Formatting gateway..."
	cd server/gateway && deno fmt
	@echo "Formatting workers..."
	cd server/workers && python -m black src/
	@echo "✅ All code formatted!"

check: fmt lint test ## Run all checks (fmt, lint, test)
	@echo "✅ All checks completed successfully!"

# Monitoring Commands
logs: ## Show logs for all services
	docker-compose logs -f

logs-gateway: ## Show gateway logs
	docker-compose logs -f gateway

logs-workers: ## Show workers logs
	docker-compose logs -f workers

status: ## Show service status
	@echo "📊 Service Status:"
	@echo "=================="
	docker-compose ps

health: ## Check service health
	@./scripts/health-check.sh

health-quick: ## Quick health check
	@echo "🏥 Quick Health Check:"
	@echo "====================="
	@echo "Client Health:"
	@curl -f http://localhost:3000 > /dev/null 2>&1 && echo "✅ Client: Healthy" || echo "❌ Client: Unhealthy"
	@echo "Gateway Health:"
	@curl -f http://localhost:8000/health > /dev/null 2>&1 && echo "✅ Gateway: Healthy" || echo "❌ Gateway: Unhealthy"
	@echo "Database Health:"
	@docker-compose exec postgres pg_isready -U aery > /dev/null 2>&1 && echo "✅ Database: Healthy" || echo "❌ Database: Unhealthy"
	@echo "Redis Health:"
	@docker-compose exec redis redis-cli ping > /dev/null 2>&1 && echo "✅ Redis: Healthy" || echo "❌ Redis: Unhealthy"

# Individual Service Commands
client: ## Start only client service
	@echo "🌐 Starting client service..."
	docker-compose -f docker-compose.dev.yml up -d client
	@echo "✅ Client started at http://localhost:3000"

gateway: ## Start only gateway service
	@echo "🔗 Starting gateway service..."
	docker-compose -f docker-compose.dev.yml up -d postgres redis gateway
	@echo "✅ Gateway started at http://localhost:8000"

workers: ## Start only workers service
	@echo "🤖 Starting workers service..."
	docker-compose -f docker-compose.dev.yml up -d postgres redis workers
	@echo "✅ Workers started"

# Database Commands
db-reset: ## Reset database
	@echo "🗄️  Resetting database..."
	docker-compose -f docker-compose.dev.yml stop postgres
	docker-compose -f docker-compose.dev.yml rm -f postgres
	docker volume rm aery_postgres_data 2>/dev/null || true
	docker-compose -f docker-compose.dev.yml up -d postgres
	@echo "✅ Database reset complete"

db-migrate: ## Run database migrations
	@echo "🗄️  Running database migrations..."
	docker-compose -f docker-compose.dev.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/init.sql
	@echo "✅ Migrations complete"

db-seed: ## Seed development data
	@echo "🌱 Seeding development data..."
	docker-compose -f docker-compose.dev.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/dev-seed.sql
	@echo "✅ Development data seeded"

# Quick Development Shortcuts
dev-logs: ## View logs from all development services
	docker-compose -f docker-compose.dev.yml logs -f

orbstack: ## Alias for dev-orbstack
	@$(MAKE) dev-orbstack

docker: ## Alias for dev
	@$(MAKE) dev

# Monitoring
monitor: ## Start monitoring stack (Prometheus + Grafana)
	@echo "📊 Starting monitoring stack..."
	docker-compose -f docker-compose.dev.yml up -d prometheus grafana
	@echo "✅ Monitoring available at:"
	@echo "   Prometheus: http://localhost:9090"
	@echo "   Grafana:    http://localhost:3001"

# Cleanup Commands
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	docker-compose down
	docker-compose -f docker-compose.dev.yml down
	docker-compose -f docker-compose.orbstack.yml down
	@echo "✅ All services stopped!"

clean: ## Stop and remove containers, networks, volumes
	@echo "🧹 Cleaning up..."
	docker-compose down -v --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --remove-orphans
	docker-compose -f docker-compose.orbstack.yml down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup completed!"

reset: clean ## Complete reset (clean + remove images)
	@echo "🔄 Resetting everything..."
	docker-compose down -v --rmi all --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --rmi all --remove-orphans
	docker-compose -f docker-compose.orbstack.yml down -v --rmi all --remove-orphans
	docker system prune -af
	@echo "✅ Complete reset completed!"

# OrbStack specific commands
orbstack-setup: ## Setup OrbStack environment
	@echo "🔧 Setting up OrbStack environment..."
	@echo "Adding local domain aliases..."
	@echo "127.0.0.1 client.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 gateway.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 api.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 pgadmin.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 redis-ui.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 mail.orb.local" | sudo tee -a /etc/hosts
	@echo "✅ OrbStack environment setup completed!"

orbstack-cleanup: ## Cleanup OrbStack environment
	@echo "🧹 Cleaning up OrbStack environment..."
	@echo "Removing local domain aliases..."
	sudo sed -i '' '/orb.local/d' /etc/hosts
	@echo "✅ OrbStack environment cleanup completed!"
