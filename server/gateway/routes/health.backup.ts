// AERY Health Check API - Simplified version
import { HandlerContext } from "$fresh/server.ts";
import { db } from "../src/lib/database.ts";
import { redis } from "../src/lib/redis.ts";

/**
 * Health check endpoint
 */
export const handler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  try {
    const startTime = Date.now();

    // Verificar servicios básicos
    let dbStatus = "unknown";
    let redisStatus = "unknown";

    try {
      // Test database connection
      const result = await db.query("SELECT 1 as test");
      dbStatus = result ? "healthy" : "unhealthy";
    } catch (error) {
      console.error("Database check failed:", error);
      dbStatus = "unhealthy";
    }

    try {
      // Test redis connection
      const isHealthy = await redis.healthCheck();
      redisStatus = isHealthy ? "healthy" : "unhealthy";
    } catch (error) {
      console.error("Redis check failed:", error);
      redisStatus = "unhealthy";
    }

    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      uptime: Math.floor(Date.now() / 1000), // Simple uptime approximation
      responseTime: Date.now() - startTime,
      services: {
        database: { status: dbStatus },
        redis: { status: redisStatus },
        api: { status: "healthy" },
      },
    };

    // Determinar estado general
    const hasFailures = dbStatus !== "healthy" || redisStatus !== "healthy";
    if (hasFailures) {
      health.status = "degraded";
    }

    const status = health.status === "healthy" ? 200 : 503;

    return new Response(JSON.stringify(health, null, 2), {
      status,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

  } catch (error) {
    console.error("❌ Error en health check:", error);
    return new Response(
      JSON.stringify({
        status: "error",
        timestamp: new Date().toISOString(),
        error: error.message,
      }, null, 2),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      }
    );
  }
};

    return Utils.createSuccessResponse(health, undefined, status);
  } catch (error) {
    console.error("❌ Error en health check:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "HEALTH_CHECK_ERROR",
      500,
      { error: error.message },
    );
  }
};

/**
 * Verificar conexión a base de datos
 */
async function checkDatabase(): Promise<
  { status: string; responseTime: number; details?: any }
> {
  const startTime = Date.now();

  try {
    await db.healthCheck();

    return {
      status: "healthy",
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      details: { error: error.message },
    };
  }
}

/**
 * Verificar conexión a Redis
 */
async function checkRedis(): Promise<
  { status: string; responseTime: number; details?: any }
> {
  const startTime = Date.now();

  try {
    await redis.ping();

    return {
      status: "healthy",
      responseTime: Date.now() - startTime,
    };
  } catch (error) {
    return {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      details: { error: error.message },
    };
  }
}

/**
 * Verificar estado de las colas
 */
async function checkQueue(): Promise<
  { status: string; responseTime: number; details?: any }
> {
  const startTime = Date.now();

  try {
    const stats = await QueueService.getQueueStats();

    return {
      status: "healthy",
      responseTime: Date.now() - startTime,
      details: {
        pending: stats.pending,
        priority: stats.priority,
        processing: stats.processing,
      },
    };
  } catch (error) {
    return {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      details: { error: error.message },
    };
  }
}

/**
 * Verificar workers activos
 */
async function checkWorkers(): Promise<
  { status: string; responseTime: number; details?: any }
> {
  const startTime = Date.now();

  try {
    const workers = await QueueService.getActiveWorkers();
    const activeCount = workers.filter((w) => w.status === "active").length;

    return {
      status: activeCount > 0 ? "healthy" : "degraded",
      responseTime: Date.now() - startTime,
      details: {
        total: workers.length,
        active: activeCount,
        workers: workers.map((w) => ({
          id: w.id,
          status: w.status,
          lastHeartbeat: w.lastHeartbeat,
        })),
      },
    };
  } catch (error) {
    return {
      status: "unhealthy",
      responseTime: Date.now() - startTime,
      details: { error: error.message },
    };
  }
}

/**
 * Extraer resultado de verificación
 */
function getCheckResult(check: PromiseSettledResult<any>): any {
  if (check.status === "fulfilled") {
    return check.value;
  } else {
    return {
      status: "unhealthy",
      responseTime: 0,
      details: { error: check.reason?.message || "Unknown error" },
    };
  }
}
