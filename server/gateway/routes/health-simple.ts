// Simple Health Check API for Testing
import { HandlerContext } from "$fresh/server.ts";
import { db } from "../src/lib/database.ts";
import { redis } from "../src/lib/redis.ts";

/**
 * Simple health check endpoint for testing
 */
export const handler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  try {
    const startTime = Date.now();

    // Verificar servicios básicos
    let dbStatus = "unknown";
    let redisStatus = "unknown";

    try {
      // Test database
      await db.queryArray("SELECT 1");
      dbStatus = "healthy";
    } catch (error) {
      console.error("Database check failed:", error);
      dbStatus = "unhealthy";
    }

    try {
      // Test redis
      const isHealthy = await redis.healthCheck();
      redisStatus = isHealthy ? "healthy" : "unhealthy";
    } catch (error) {
      console.error("Redis check failed:", error);
      redisStatus = "unhealthy";
    }

    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      responseTime: Date.now() - startTime,
      services: {
        database: { status: dbStatus },
        redis: { status: redisStatus },
      },
    };

    // Determinar estado general
    const hasFailures = dbStatus !== "healthy" || redisStatus !== "healthy";
    if (hasFailures) {
      health.status = "degraded";
    }

    const status = health.status === "healthy" ? 200 : 503;

    return new Response(JSON.stringify(health, null, 2), {
      status,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

  } catch (error) {
    console.error("❌ Error en health check:", error);
    return new Response(
      JSON.stringify({
        status: "error",
        timestamp: new Date().toISOString(),
        error: error.message,
      }, null, 2),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      }
    );
  }
};
