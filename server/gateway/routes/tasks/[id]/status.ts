// AERY Task Status API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../../../lib/auth.ts";
import { QueueService } from "../../../lib/queue.ts";
import { db } from "../../../lib/database.ts";
import { Utils } from "../../../lib/utils.ts";

/**
 * Get task status endpoint
 */
export const handler = async (
  req: Request,
  ctx: HandlerContext,
): Promise<Response> => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization, X-API-Key",
        "Access-Control-Max-Age": "86400",
      },
    });
  }

  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, X-API-Key",
  };

  if (req.method !== "GET") {
    return new Response(JSON.stringify({
      success: false,
      error: {
        message: "Método no permitido",
        code: "METHOD_NOT_ALLOWED",
      },
    }), {
      status: 405,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  }

  try {
    // Autenticación
    const user = await authenticateRequest(req);
    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        error: {
          message: "No autorizado",
          code: "UNAUTHORIZED",
        },
      }), {
        status: 401,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    const taskId = ctx.params.id;
    if (!taskId) {
      return new Response(JSON.stringify({
        success: false,
        error: {
          message: "ID de tarea requerido",
          code: "MISSING_TASK_ID",
        },
      }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    // Obtener estado de la cola
    const queueTask = await QueueService.getTaskStatus(taskId);

    // Obtener ejecución de la base de datos
    const execution = queueTask
      ? await db.getTaskExecutionByTaskId(taskId)
      : null;

    if (!queueTask && !execution) {
      return new Response(JSON.stringify({
        success: false,
        error: {
          message: "Tarea no encontrada",
          code: "TASK_NOT_FOUND",
        },
      }), {
        status: 404,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    // Verificar permisos
    if (
      execution && execution.userId !== user.id &&
      !AuthService.hasPermission(user, "view_all_executions")
    ) {
      return new Response(JSON.stringify({
        success: false,
        error: {
          message: "Sin permisos para ver esta tarea",
          code: "INSUFFICIENT_PERMISSIONS",
        },
      }), {
        status: 403,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    // Construir respuesta
    const response: any = {
      taskId,
      status: queueTask?.status || execution?.status || "unknown",
      createdAt: queueTask?.createdAt || execution?.createdAt,
      startedAt: queueTask?.startedAt,
      completedAt: queueTask?.completedAt || execution?.completedAt,
      instruction: queueTask?.instruction || execution?.instruction,
      url: queueTask?.url || execution?.url,
      priority: queueTask?.priority || execution?.priority || false,
      retryCount: queueTask?.retryCount || 0,
      workerId: queueTask?.workerId,
    };

    // Agregar información de ejecución si está disponible
    if (execution) {
      response.executionId = execution.id;
      response.userId = execution.userId;
      response.prescriptId = execution.prescriptId;
      response.timeout = execution.timeout;
      response.screenshots = execution.screenshots;
      response.headless = execution.headless;
      response.viewport = execution.viewport;
      response.userAgent = execution.userAgent;
      response.metadata = execution.metadata;
    }

    // Agregar resultado si está completada
    if (queueTask?.result) {
      response.result = {
        success: queueTask.result.success,
        executionTime: queueTask.result.executionTime,
        screenshots: queueTask.result.screenshots || [],
        logs: queueTask.result.logs || [],
        error: queueTask.result.error,
        data: queueTask.result.data,
      };
    }

    // Agregar información de progreso si está en proceso
    if (response.status === "processing") {
      const progress = await getTaskProgress(taskId);
      if (progress) {
        response.progress = progress;
      }
    }

    // Agregar estimación de tiempo restante
    if (["pending", "processing"].includes(response.status)) {
      response.estimatedTimeRemaining = await getEstimatedTimeRemaining(
        taskId,
        response.status,
      );
    }

    return Utils.createSuccessResponse(response);
  } catch (error) {
    console.error("❌ Error obteniendo estado de tarea:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
};

/**
 * Autenticar solicitud
 */
async function authenticateRequest(req: Request) {
  // Intentar autenticación por API Key
  const apiKey = req.headers.get("X-API-Key") ||
    req.headers.get("Authorization")?.replace("Bearer ", "");

  if (apiKey) {
    const user = await AuthService.authenticateApiKey(apiKey);
    if (user) return user;
  }

  // Intentar autenticación por JWT
  const authHeader = req.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.substring(7);
    const user = await AuthService.authenticateJwt(token);
    if (user) return user;
  }

  return null;
}

/**
 * Obtener progreso de la tarea
 */
async function getTaskProgress(taskId: string): Promise<any | null> {
  try {
    // Intentar obtener progreso de Redis
    const progressData = await QueueService.redis.get(
      `aery:progress:${taskId}`,
    );
    if (progressData) {
      return JSON.parse(progressData);
    }
    return null;
  } catch (error) {
    console.error("❌ Error obteniendo progreso:", error);
    return null;
  }
}

/**
 * Estimar tiempo restante
 */
async function getEstimatedTimeRemaining(
  taskId: string,
  status: string,
): Promise<number | null> {
  try {
    if (status === "pending") {
      // Estimar basado en posición en cola
      const stats = await QueueService.getQueueStats();
      const avgExecutionTime = 60000; // 1 minuto promedio
      return stats.pending * avgExecutionTime;
    }

    if (status === "processing") {
      // Estimar basado en tiempo transcurrido
      const task = await QueueService.getTaskStatus(taskId);
      if (task?.startedAt) {
        const elapsed = Date.now() - new Date(task.startedAt).getTime();
        const avgExecutionTime = 120000; // 2 minutos promedio
        return Math.max(0, avgExecutionTime - elapsed);
      }
    }

    return null;
  } catch (error) {
    return null;
  }
}
