// AERY Health Check API - Simplified version
import { HandlerContext } from "$fresh/server.ts";

/**
 * Simple health check endpoint
 */
export const handler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  try {
    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      uptime: Math.floor(Date.now() / 1000),
      services: {
        api: { status: "healthy" },
        database: { status: "healthy" },
        redis: { status: "healthy" },
      },
    };

    return new Response(JSON.stringify(health, null, 2), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

  } catch (error) {
    console.error("❌ Error en health check:", error);
    return new Response(
      JSON.stringify({
        status: "error",
        timestamp: new Date().toISOString(),
        error: error?.message || "Unknown error",
      }, null, 2),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache",
        },
      }
    );
  }
};
