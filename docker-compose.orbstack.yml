# OrbStack Optimized Docker Compose
# Leverages OrbStack's fast file sharing and networking

services:

  # API Gateway (OrbStack Optimized)
  gateway:
    build:
      context: .
      dockerfile: ./server/gateway/Dockerfile
    container_name: aery_gateway_orbstack
    ports:
      - "8000:8000"
    volumes:
      # OrbStack optimized volume mounts
      - type: bind
        source: ./server/gateway
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
      - gateway_cache:/app/.deno
    environment:
      - DATABASE_URL=postgresql://aery:<EMAIL>:5432/aery
      - REDIS_URL=redis://redis.orb.local:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - API_SECRET_KEY=${API_SECRET_KEY:-dev_secret_key}
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret}
      - DENO_ENV=development
      - LOG_LEVEL=debug
      - ORBSTACK_OPTIMIZED=true
    networks:
      aery_orbstack_network:
        aliases:
          - gateway.orb.local
          - api.orb.local
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: ["deno", "task", "dev"]

  # Python Workers (OrbStack Optimized)
  workers:
    build:
      context: .
      dockerfile: ./server/workers/Dockerfile
    container_name: aery_workers_orbstack
    ports:
      - "5678:5678" # Debug port
    volumes:
      # OrbStack optimized volume mounts
      - type: bind
        source: ./server/workers
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
      - type: bind
        source: ./artifacts
        target: /app/artifacts
        consistency: delegated
      - /dev/shm:/dev/shm
      - workers_cache:/app/.cache
    environment:
      - REDIS_URL=redis://redis.orb.local:6379
      - DATABASE_URL=postgresql://aery:<EMAIL>:5432/aery
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PLAYWRIGHT_HEADLESS=false
      - PYTHON_ENV=development
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - ORBSTACK_OPTIMIZED=true
    networks:
      aery_orbstack_network:
        aliases:
          - workers.orb.local
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    command: ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "--wait-for-client", "main.py"]

  # PostgreSQL Database (OrbStack Optimized)
  postgres:
    image: postgres:15-alpine
    container_name: aery_postgres_orbstack
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=aery
      - POSTGRES_USER=aery
      - POSTGRES_PASSWORD=aery_dev_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_orbstack_data:/var/lib/postgresql/data
      - type: bind
        source: ./shared/database/init.sql
        target: /docker-entrypoint-initdb.d/init.sql
        consistency: cached
      - type: bind
        source: ./shared/database/dev-seed.sql
        target: /docker-entrypoint-initdb.d/dev-seed.sql
        consistency: cached
    networks:
      aery_orbstack_network:
        aliases:
          - postgres.orb.local
          - db.orb.local
    restart: unless-stopped
    command: postgres -c log_statement=all -c log_destination=stderr

  # Redis (OrbStack Optimized)
  redis:
    image: redis:7-alpine
    container_name: aery_redis_orbstack
    ports:
      - "6379:6379"
    volumes:
      - redis_orbstack_data:/data
    networks:
      aery_orbstack_network:
        aliases:
          - redis.orb.local
          - cache.orb.local
    command: redis-server --appendonly yes --loglevel verbose
    restart: unless-stopped

  # Development Tools (OrbStack Optimized)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aery_redis_commander_orbstack
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis.orb.local:6379
    networks:
      aery_orbstack_network:
        aliases:
          - redis-ui.orb.local
    depends_on:
      - redis
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aery_pgadmin_orbstack
    ports:
      - "8080:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_orbstack_data:/var/lib/pgadmin
    networks:
      aery_orbstack_network:
        aliases:
          - pgadmin.orb.local
          - db-ui.orb.local
    depends_on:
      - postgres
    restart: unless-stopped

  mailhog:
    image: mailhog/mailhog:latest
    container_name: aery_mailhog_orbstack
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      aery_orbstack_network:
        aliases:
          - mail.orb.local
    restart: unless-stopped

volumes:
  postgres_orbstack_data:
    driver: local
  redis_orbstack_data:
    driver: local
  pgadmin_orbstack_data:
    driver: local
  gateway_cache:
    driver: local
  workers_cache:
    driver: local

networks:
  aery_orbstack_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: aery-orbstack
